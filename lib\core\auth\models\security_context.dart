import 'package:freezed_annotation/freezed_annotation.dart';

import 'auth_state.dart';

part 'security_context.freezed.dart';

/// Security Context model following Context7 MCP best practices
///
/// Provides comprehensive security information for authenticated sessions
/// including device trust, permissions, and assurance levels.

@freezed
abstract class SecurityContext with _$SecurityContext {
  /// Creates a security context for authentication and authorization.
  ///
  /// [sessionId] - Unique identifier for the user session
  /// [userId] - Unique identifier for the authenticated user
  /// [createdAt] - Timestamp when the security context was created
  /// [lastActivity] - Timestamp of the last user activity
  /// [expiresAt] - Timestamp when the security context expires
  /// [deviceTrust] - Trust level assessment of the user's device
  /// [assuranceLevel] - Authentication assurance level achieved
  /// [permissions] - List of permissions granted to the user
  /// [roles] - List of roles assigned to the user
  /// [ipAddress] - IP address of the client (if available)
  /// [userAgent] - User agent string of the client browser/app
  /// [geolocation] - Geographic location information (if available)
  /// [deviceInfo] - Additional device information and fingerprinting data
  /// [securityFlags] - Security-related flags and indicators
  /// [riskAssessment] - Risk assessment results for this context
  /// [metadata] - Additional context-specific metadata
  const factory SecurityContext({
    required String sessionId,
    required String userId,
    required DateTime createdAt,
    required DateTime lastActivity,
    required DateTime expiresAt,
    required DeviceTrust deviceTrust,
    required AssuranceLevel assuranceLevel,
    required List<String> permissions,
    required List<String> roles,
    String? ipAddress,
    String? userAgent,
    String? geolocation,
    Map<String, dynamic>? deviceInfo,
    List<SecurityFlag>? securityFlags,
    RiskAssessment? riskAssessment,
    Map<String, dynamic>? metadata,
  }) = _SecurityContext;
}

/// Security flags for additional context
@freezed
abstract class SecurityFlag with _$SecurityFlag {
  const factory SecurityFlag({
    required SecurityFlagType type,
    required String description,
    required SecuritySeverity severity,
    required DateTime detectedAt,
    DateTime? resolvedAt,
    Map<String, dynamic>? metadata,
  }) = _SecurityFlag;
}

/// Types of security flags that can be raised during authentication.
///
/// These flags indicate potential security concerns or anomalies
/// that may require additional verification or monitoring.
enum SecurityFlagType {
  /// User is accessing from an unusual or suspicious geographic location
  suspiciousLocation,

  /// User is using a device that hasn't been seen before
  newDevice,

  /// Detected unusual patterns in user behavior or activity
  unusualActivity,

  /// Multiple failed authentication attempts detected
  multipleFailedAttempts,

  /// User's password has been found in known breach databases
  passwordCompromised,

  /// Indicators suggest the account may be shared between multiple users
  accountSharing,

  /// Detected automated or bot-like behavior patterns
  automatedBehavior,

  /// User is connecting through a VPN service
  vpnDetected,

  /// User is connecting through the Tor anonymity network
  torDetected,

  /// Malware or suspicious software detected on user's device
  malwareDetected,
}

/// Security severity levels for risk assessment and alerting.
///
/// Defines the severity classification for security events,
/// flags, and risk assessments in the authentication system.
enum SecuritySeverity {
  /// Informational level - no immediate action required
  info,

  /// Low severity - minor security concern
  low,

  /// Medium severity - moderate security risk requiring attention
  medium,

  /// High severity - significant security risk requiring immediate attention
  high,

  /// Critical severity - severe security threat requiring urgent action
  critical,
}

/// Comprehensive risk assessment for an authentication session.
///
/// Evaluates various risk factors to determine the overall security
/// risk level and provide recommendations for additional security measures.
@freezed
abstract class RiskAssessment with _$RiskAssessment {
  /// Creates a risk assessment result.
  ///
  /// [overallRisk] - Overall risk level classification
  /// [riskScore] - Numerical risk score (0.0 to 1.0, higher is riskier)
  /// [assessedAt] - Timestamp when the assessment was performed
  /// [riskFactors] - List of individual risk factors identified
  /// [recommendation] - Recommended security actions based on assessment
  /// [details] - Additional assessment details and metadata
  const factory RiskAssessment({
    required RiskLevel overallRisk,
    required double riskScore,
    required DateTime assessedAt,
    required List<RiskFactor> riskFactors,
    String? recommendation,
    Map<String, dynamic>? details,
  }) = _RiskAssessment;
}

/// Risk level classifications for security assessment.
///
/// Defines the overall risk level categories used in
/// authentication and authorization decisions.
enum RiskLevel {
  /// Very low risk - minimal security concerns
  veryLow,

  /// Low risk - minor security considerations
  low,

  /// Medium risk - moderate security concerns requiring attention
  medium,

  /// High risk - significant security concerns requiring additional verification
  high,

  /// Very high risk - severe security concerns requiring strict controls
  veryHigh,
}

/// Individual risk factor contributing to overall risk assessment.
///
/// Represents a specific security concern or anomaly that contributes
/// to the overall risk calculation for an authentication session.
@freezed
abstract class RiskFactor with _$RiskFactor {
  /// Creates a risk factor.
  ///
  /// [type] - The category/type of this risk factor
  /// [weight] - Importance weight of this factor (0.0 to 1.0)
  /// [score] - Risk score for this specific factor (0.0 to 1.0)
  /// [description] - Human-readable description of the risk
  /// [details] - Additional factor-specific details and metadata
  const factory RiskFactor({
    required RiskFactorType type,
    required double weight,
    required double score,
    required String description,
    Map<String, dynamic>? details,
  }) = _RiskFactor;
}

/// Categories of risk factors used in security assessment.
///
/// Defines the different types of security risks that can be
/// evaluated during authentication and authorization processes.
enum RiskFactorType {
  /// Risk based on device trust level and recognition
  deviceTrust,

  /// Risk from unusual or suspicious geographic location
  locationAnomaly,

  /// Risk from unusual user behavior patterns
  behaviorAnomaly,

  /// Risk from unusual timing patterns (e.g., off-hours access)
  timeAnomaly,

  /// Risk from network characteristics (VPN, Tor, etc.)
  networkRisk,

  /// Risk based on account age and maturity
  accountAge,

  /// Risk from authentication history and patterns
  authenticationHistory,

  /// Risk from device fingerprinting analysis
  deviceFingerprint,

  /// Risk from IP address reputation and history
  ipReputation,

  /// Risk from velocity and frequency of requests
  velocityCheck,
}

/// Comprehensive device information for security analysis.
///
/// Collects device characteristics and properties used for
/// device fingerprinting and security risk assessment.
@freezed
abstract class DeviceInfo with _$DeviceInfo {
  /// Creates device information for security context.
  ///
  /// [deviceId] - Unique identifier for the device
  /// [platform] - Operating system platform (iOS, Android, etc.)
  /// [osVersion] - Operating system version
  /// [appVersion] - Application version
  /// [deviceModel] - Device model name
  /// [manufacturer] - Device manufacturer
  /// [screenResolution] - Screen resolution information
  /// [timezone] - Device timezone setting
  /// [language] - Device language setting
  /// [isJailbroken] - Whether device is jailbroken/rooted
  /// [isEmulator] - Whether running on an emulator/simulator
  /// [hasVpn] - Whether VPN is active on the device
  /// [installedApps] - List of installed applications (if available)
  /// [additionalInfo] - Additional device-specific information
  const factory DeviceInfo({
    required String deviceId,
    required String platform,
    required String osVersion,
    required String appVersion,
    String? deviceModel,
    String? manufacturer,
    String? screenResolution,
    String? timezone,
    String? language,
    bool? isJailbroken,
    bool? isEmulator,
    bool? hasVpn,
    List<String>? installedApps,
    Map<String, dynamic>? additionalInfo,
  }) = _DeviceInfo;
}

/// Session security metrics
@freezed
abstract class SessionSecurityMetrics with _$SessionSecurityMetrics {
  const factory SessionSecurityMetrics({
    required int totalRequests,
    required int failedAttempts,
    required DateTime firstActivity,
    required DateTime lastActivity,
    required List<String> accessedResources,
    required List<String> ipAddresses,
    required List<String> userAgents,
    int? suspiciousActivities,
    double? averageRequestInterval,
    Map<String, int>? resourceAccessCounts,
  }) = _SessionSecurityMetrics;
}

/// Security policy configuration
@freezed
abstract class SecurityPolicy with _$SecurityPolicy {
  const factory SecurityPolicy({
    required Duration sessionTimeout,
    required Duration maxSessionDuration,
    required int maxConcurrentSessions,
    required bool requireMfa,
    required List<String> requiredPermissions,
    required AssuranceLevel minimumAssuranceLevel,
    required RiskLevel maxAllowedRisk,
    @Default(true) bool enforceDeviceTrust,
    @Default(true) bool enableRiskAssessment,
    @Default(true) bool logSecurityEvents,
    Map<String, dynamic>? customRules,
  }) = _SecurityPolicy;
}

/// Security event for audit logging
@freezed
abstract class SecurityEvent with _$SecurityEvent {
  const factory SecurityEvent({
    required String id,
    required SecurityEventType type,
    required DateTime timestamp,
    required String sessionId,
    String? userId,
    String? deviceId,
    String? ipAddress,
    String? userAgent,
    String? resource,
    String? action,
    SecuritySeverity? severity,
    String? description,
    Map<String, dynamic>? metadata,
  }) = _SecurityEvent;
}

/// Types of security events
enum SecurityEventType {
  sessionCreated,
  sessionExpired,
  sessionTerminated,
  permissionGranted,
  permissionDenied,
  resourceAccessed,
  suspiciousActivity,
  riskAssessmentTriggered,
  securityFlagRaised,
  deviceTrustChanged,
  locationChanged,
  authenticationFailed,
  authenticationSucceeded,
  mfaRequired,
  mfaCompleted,
  accountLocked,
  accountUnlocked,
}

/// Extensions for SecurityContext
extension SecurityContextExtensions on SecurityContext {
  /// Check if session is still valid
  bool get isValid => DateTime.now().isBefore(expiresAt);

  /// Check if session is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Get time until expiry
  Duration get timeUntilExpiry => expiresAt.difference(DateTime.now());

  /// Check if session needs refresh
  bool get needsRefresh {
    final now = DateTime.now();
    final refreshThreshold = expiresAt.subtract(const Duration(minutes: 5));
    return now.isAfter(refreshThreshold);
  }

  /// Check if device is trusted
  bool get isDeviceTrusted => deviceTrust.trustLevel.index >= TrustLevel.trusted.index;

  /// Check if session has high assurance
  bool get hasHighAssurance => assuranceLevel.index >= AssuranceLevel.high.index;

  /// Get active security flags
  List<SecurityFlag> get activeSecurityFlags => securityFlags?.where((flag) => flag.resolvedAt == null).toList() ?? [];

  /// Check if session has critical security flags
  bool get hasCriticalFlags => activeSecurityFlags.any((flag) => flag.severity == SecuritySeverity.critical);

  /// Get current risk level
  RiskLevel? get currentRiskLevel => riskAssessment?.overallRisk;

  /// Check if risk is acceptable
  bool get isRiskAcceptable => currentRiskLevel == null || currentRiskLevel!.index <= RiskLevel.medium.index;

  /// Get session duration
  Duration get sessionDuration => DateTime.now().difference(createdAt);

  /// Get idle time
  Duration get idleTime => DateTime.now().difference(lastActivity);

  /// Check if session is idle
  bool get isIdle => idleTime.inMinutes > 30;

  /// Check if user has permission
  bool hasPermission(String permission) => permissions.contains(permission);

  /// Check if user has role
  bool hasRole(String role) => roles.contains(role);

  /// Check if user has any of the specified permissions
  bool hasAnyPermission(List<String> requiredPermissions) =>
      requiredPermissions.any((permission) => permissions.contains(permission));

  /// Check if user has all of the specified permissions
  bool hasAllPermissions(List<String> requiredPermissions) =>
      requiredPermissions.every((permission) => permissions.contains(permission));

  /// Get security score (0-100)
  double get securityScore {
    var score = 50.0; // Base score

    // Device trust contribution (0-30 points)
    score += deviceTrust.trustLevel.index * 6.0;

    // Assurance level contribution (0-20 points)
    score += assuranceLevel.index * 4.0;

    // Risk assessment contribution (-20 to +10 points)
    if (riskAssessment != null) {
      final riskPenalty = riskAssessment!.overallRisk.index * 5.0;
      score -= riskPenalty;
    }

    // Security flags penalty (-5 points per active flag)
    score -= activeSecurityFlags.length * 5.0;

    // Session age bonus (up to 10 points for established sessions)
    final sessionAgeHours = sessionDuration.inHours;
    if (sessionAgeHours > 0 && sessionAgeHours <= 24) {
      score += (sessionAgeHours / 24.0) * 10.0;
    }

    return score.clamp(0.0, 100.0);
  }
}

/// Extensions for RiskLevel
extension RiskLevelExtensions on RiskLevel {
  /// Get human-readable name
  String get displayName {
    switch (this) {
      case RiskLevel.veryLow:
        return 'Very Low';
      case RiskLevel.low:
        return 'Low';
      case RiskLevel.medium:
        return 'Medium';
      case RiskLevel.high:
        return 'High';
      case RiskLevel.veryHigh:
        return 'Very High';
    }
  }

  /// Get color representation
  String get colorCode {
    switch (this) {
      case RiskLevel.veryLow:
        return '#4CAF50'; // Green
      case RiskLevel.low:
        return '#8BC34A'; // Light Green
      case RiskLevel.medium:
        return '#FF9800'; // Orange
      case RiskLevel.high:
        return '#F44336'; // Red
      case RiskLevel.veryHigh:
        return '#9C27B0'; // Purple
    }
  }

  /// Check if risk requires action
  bool get requiresAction => index >= RiskLevel.high.index;
}

/// Extensions for AssuranceLevel
extension AssuranceLevelExtensions on AssuranceLevel {
  /// Get human-readable name
  String get displayName {
    switch (this) {
      case AssuranceLevel.none:
        return 'None';
      case AssuranceLevel.low:
        return 'Low';
      case AssuranceLevel.medium:
        return 'Medium';
      case AssuranceLevel.high:
        return 'High';
      case AssuranceLevel.veryHigh:
        return 'Very High';
    }
  }

  /// Get description
  String get description {
    switch (this) {
      case AssuranceLevel.none:
        return 'No additional authentication';
      case AssuranceLevel.low:
        return 'Basic password authentication';
      case AssuranceLevel.medium:
        return 'Password with device verification';
      case AssuranceLevel.high:
        return 'Multi-factor authentication';
      case AssuranceLevel.veryHigh:
        return 'Strong multi-factor with biometrics';
    }
  }

  /// Check if level meets minimum requirement
  bool meetsMinimum(AssuranceLevel minimum) => index >= minimum.index;
}
